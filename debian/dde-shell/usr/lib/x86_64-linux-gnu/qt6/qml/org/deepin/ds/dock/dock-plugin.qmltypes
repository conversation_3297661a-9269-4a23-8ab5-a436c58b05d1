import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "pluginmanagerextension_p.h"
        name: "PluginManager"
        accessSemantics: "reference"
        exports: ["org.deepin.ds.dock/PluginManager 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "dockPosition"
            type: "uint"
            read: "dockPosition"
            write: "setDockPosition"
            index: 0
        }
        Property {
            name: "dockColorTheme"
            type: "uint"
            read: "dockColorTheme"
            write: "setDockColorTheme"
            index: 1
        }
        Property {
            name: "dockSize"
            type: "QSize"
            read: "dockSize"
            write: "setDockSize"
            notify: "dockSizeChanged"
            index: 2
            isFinal: true
        }
        Signal {
            name: "pluginPopupCreated"
            Parameter { type: "PluginPopup"; isPointer: true }
        }
        Signal { name: "pluginCloseQuickPanelPopup" }
        Signal {
            name: "pluginSurfaceCreated"
            Parameter { type: "PluginSurface"; isPointer: true }
        }
        Signal {
            name: "pluginSurfaceDestroyed"
            Parameter { type: "PluginSurface"; isPointer: true }
        }
        Signal {
            name: "messageRequest"
            Parameter { type: "PluginSurface"; isPointer: true }
            Parameter { name: "msg"; type: "QString" }
        }
        Signal { name: "dockSizeChanged" }
        Signal {
            name: "requestShutdown"
            Parameter { name: "type"; type: "QString" }
        }
        Method { name: "onFontChanged" }
        Method { name: "onActiveColorChanged" }
        Method { name: "onThemeChanged" }
        Method {
            name: "updateDockOverflowState"
            Parameter { name: "state"; type: "int" }
        }
        Method {
            name: "setPopupMinHeight"
            Parameter { name: "height"; type: "int" }
        }
    }
    Component {
        file: "pluginmanagerextension_p.h"
        name: "PluginScaleManager"
        accessSemantics: "reference"
        exports: ["org.deepin.ds.dock/PluginScaleManager 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "pluginScale"
            type: "uint"
            read: "pluginScale"
            write: "setPluginScale"
            notify: "pluginScaleChanged"
            index: 0
        }
        Signal {
            name: "pluginScaleChanged"
            Parameter { name: "scale"; type: "uint" }
        }
    }
    Component {
        file: "constants.h"
        name: "dock"
        accessSemantics: "none"
        exports: ["org.deepin.ds.dock/Dock 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        Enum {
            name: "SIZE"
            values: [
                "MIN_DOCK_SIZE",
                "DEFAULT_DOCK_SIZE",
                "MAX_DOCK_SIZE",
                "MIN_DOCK_TASKMANAGER_ICON_SIZE",
                "MAX_DOCK_TASKMANAGER_ICON_SIZE"
            ]
        }
        Enum {
            name: "IndicatorStyle"
            values: ["Fashion", "Efficient"]
        }
        Enum {
            name: "ItemAlignment"
            values: ["CenterAlignment", "LeftAlignment"]
        }
        Enum {
            name: "ColorTheme"
            values: ["Light", "Dark"]
        }
        Enum {
            name: "HideMode"
            values: ["KeepShowing", "KeepHidden", "SmartHide"]
        }
        Enum {
            name: "Position"
            values: ["Top", "Right", "Bottom", "Left"]
        }
        Enum {
            name: "HideState"
            values: ["Unknown", "Show", "Hide"]
        }
        Enum {
            name: "AniAction"
            values: ["AA_Show", "AA_Hide"]
        }
        Enum {
            name: "TrayPopupType"
            values: [
                "TrayPopupTypePanel",
                "TrayPopupTypeTooltip",
                "TrayPopupTypeMenu",
                "TrayPopupTypeEmbed",
                "TrayPopupTypeSubPopup"
            ]
        }
        Enum {
            name: "TrayPluginType"
            values: ["Tray", "Fixed", "Quick"]
        }
        Enum {
            name: "TrayPluginSizePolicy"
            values: ["System", "Custom"]
        }
        Enum {
            name: "OverFlowState"
            values: ["OverflowNotExist", "OverflowExist", "OverflowAll"]
        }
    }
    Component {
        file: "dockpositioner.h"
        name: "dock::DockPanelPositioner"
        accessSemantics: "reference"
        prototype: "dock::DockPositioner"
        exports: ["org.deepin.ds.dock/DockPanelPositioner 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        attachedType: "dock::DockPanelPositioner"
        Property {
            name: "horizontalOffset"
            type: "int"
            read: "horizontalOffset"
            write: "setHorizontalOffset"
            reset: "resetHorizontalOffset"
            notify: "horizontalOffsetChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "vertialOffset"
            type: "int"
            read: "vertialOffset"
            write: "setVertialOffset"
            reset: "resetVertialOffset"
            notify: "vertialOffsetChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "horizontalOffsetChanged" }
        Signal { name: "vertialOffsetChanged" }
        Method { name: "updatePosition" }
    }
    Component {
        file: "dockpositioner.h"
        name: "dock::DockPositioner"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.ds.dock/DockPositioner 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        attachedType: "dock::DockPositioner"
        Property {
            name: "bounding"
            type: "QRect"
            read: "bounding"
            write: "setBounding"
            notify: "boundingChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "x"
            type: "int"
            read: "x"
            notify: "xChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "y"
            type: "int"
            read: "y"
            notify: "yChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "boundingChanged" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Method { name: "update" }
        Method { name: "updatePosition" }
    }
}
