import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qabstractitemmodel.h"
        name: "QAbstractItemModel"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "LayoutChangeHint"
            values: [
                "NoLayoutChangeHint",
                "VerticalSortHint",
                "HorizontalSortHint"
            ]
        }
        Enum {
            name: "CheckIndexOption"
            values: [
                "NoOption",
                "IndexIsValid",
                "DoNotUseParent",
                "ParentIsInvalid"
            ]
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "int"; isList: true }
        }
        Signal {
            name: "dataChanged"
            isCloned: true
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutChanged"; isCloned: true }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutAboutToBeChanged"; isCloned: true }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method { name: "resetInternalData" }
        Method {
            name: "hasIndex"
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int"; isCloned: true }
        Method {
            name: "columnCount"
            type: "int"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int"; isCloned: true }
        Method {
            name: "hasChildren"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool"; isCloned: true }
        Method {
            name: "data"
            type: "QVariant"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            isCloned: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            isCloned: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            isCloned: true
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "qabstractproxymodel.h"
        name: "QAbstractProxyModel"
        accessSemantics: "reference"
        prototype: "QAbstractItemModel"
        Property {
            name: "sourceModel"
            type: "QAbstractItemModel"
            isPointer: true
            bindable: "bindableSourceModel"
            read: "sourceModel"
            write: "setSourceModel"
            notify: "sourceModelChanged"
            index: 0
        }
        Signal { name: "sourceModelChanged" }
        Method { name: "_q_sourceModelDestroyed" }
        Method {
            name: "_q_sourceModelRowsAboutToBeInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_sourceModelRowsInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_sourceModelRowsRemoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_sourceModelColumnsAboutToBeInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_sourceModelColumnsInserted"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "_q_sourceModelColumnsRemoved"
            Parameter { type: "QModelIndex" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method {
            name: "mapToSource"
            type: "QModelIndex"
            Parameter { name: "proxyIndex"; type: "QModelIndex" }
        }
        Method {
            name: "mapFromSource"
            type: "QModelIndex"
            Parameter { name: "sourceIndex"; type: "QModelIndex" }
        }
        Method {
            name: "mapSelectionToSource"
            type: "QItemSelection"
            Parameter { name: "selection"; type: "QItemSelection" }
        }
        Method {
            name: "mapSelectionFromSource"
            type: "QItemSelection"
            Parameter { name: "selection"; type: "QItemSelection" }
        }
    }
    Component {
        file: "qsortfilterproxymodel.h"
        name: "QSortFilterProxyModel"
        accessSemantics: "reference"
        prototype: "QAbstractProxyModel"
        Property {
            name: "filterRegularExpression"
            type: "QRegularExpression"
            bindable: "bindableFilterRegularExpression"
            read: "filterRegularExpression"
            write: "setFilterRegularExpression"
            index: 0
        }
        Property {
            name: "filterKeyColumn"
            type: "int"
            bindable: "bindableFilterKeyColumn"
            read: "filterKeyColumn"
            write: "setFilterKeyColumn"
            index: 1
        }
        Property {
            name: "dynamicSortFilter"
            type: "bool"
            bindable: "bindableDynamicSortFilter"
            read: "dynamicSortFilter"
            write: "setDynamicSortFilter"
            index: 2
        }
        Property {
            name: "filterCaseSensitivity"
            type: "Qt::CaseSensitivity"
            bindable: "bindableFilterCaseSensitivity"
            read: "filterCaseSensitivity"
            write: "setFilterCaseSensitivity"
            notify: "filterCaseSensitivityChanged"
            index: 3
        }
        Property {
            name: "sortCaseSensitivity"
            type: "Qt::CaseSensitivity"
            bindable: "bindableSortCaseSensitivity"
            read: "sortCaseSensitivity"
            write: "setSortCaseSensitivity"
            notify: "sortCaseSensitivityChanged"
            index: 4
        }
        Property {
            name: "isSortLocaleAware"
            type: "bool"
            bindable: "bindableIsSortLocaleAware"
            read: "isSortLocaleAware"
            write: "setSortLocaleAware"
            notify: "sortLocaleAwareChanged"
            index: 5
        }
        Property {
            name: "sortRole"
            type: "int"
            bindable: "bindableSortRole"
            read: "sortRole"
            write: "setSortRole"
            notify: "sortRoleChanged"
            index: 6
        }
        Property {
            name: "filterRole"
            type: "int"
            bindable: "bindableFilterRole"
            read: "filterRole"
            write: "setFilterRole"
            notify: "filterRoleChanged"
            index: 7
        }
        Property {
            name: "recursiveFilteringEnabled"
            type: "bool"
            bindable: "bindableRecursiveFilteringEnabled"
            read: "isRecursiveFilteringEnabled"
            write: "setRecursiveFilteringEnabled"
            notify: "recursiveFilteringEnabledChanged"
            index: 8
        }
        Property {
            name: "autoAcceptChildRows"
            type: "bool"
            bindable: "bindableAutoAcceptChildRows"
            read: "autoAcceptChildRows"
            write: "setAutoAcceptChildRows"
            notify: "autoAcceptChildRowsChanged"
            index: 9
        }
        Signal {
            name: "dynamicSortFilterChanged"
            Parameter { name: "dynamicSortFilter"; type: "bool" }
        }
        Signal {
            name: "filterCaseSensitivityChanged"
            Parameter { name: "filterCaseSensitivity"; type: "Qt::CaseSensitivity" }
        }
        Signal {
            name: "sortCaseSensitivityChanged"
            Parameter { name: "sortCaseSensitivity"; type: "Qt::CaseSensitivity" }
        }
        Signal {
            name: "sortLocaleAwareChanged"
            Parameter { name: "sortLocaleAware"; type: "bool" }
        }
        Signal {
            name: "sortRoleChanged"
            Parameter { name: "sortRole"; type: "int" }
        }
        Signal {
            name: "filterRoleChanged"
            Parameter { name: "filterRole"; type: "int" }
        }
        Signal {
            name: "recursiveFilteringEnabledChanged"
            Parameter { name: "recursiveFilteringEnabled"; type: "bool" }
        }
        Signal {
            name: "autoAcceptChildRowsChanged"
            Parameter { name: "autoAcceptChildRows"; type: "bool" }
        }
        Method {
            name: "setFilterRegularExpression"
            Parameter { name: "pattern"; type: "QString" }
        }
        Method {
            name: "setFilterRegularExpression"
            Parameter { name: "regularExpression"; type: "QRegularExpression" }
        }
        Method {
            name: "setFilterWildcard"
            Parameter { name: "pattern"; type: "QString" }
        }
        Method {
            name: "setFilterFixedString"
            Parameter { name: "pattern"; type: "QString" }
        }
        Method { name: "invalidate" }
    }
    Component {
        file: "quickpanelproxymodel.h"
        name: "dock::QuickPanelProxyModel"
        accessSemantics: "reference"
        prototype: "QSortFilterProxyModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "org.deepin.ds.dock.tray.quickpanel/QuickPanelProxyModel 1.0"
        ]
        exportMetaObjectRevisions: [256]
        Property {
            name: "trayItemPluginId"
            type: "QString"
            read: "trayItemPluginId"
            write: "setTrayItemPluginId"
            notify: "trayItemPluginIdChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "trayItemSurface"
            type: "QObject"
            isPointer: true
            read: "trayItemSurface"
            notify: "trayItemSurfaceChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "trayPluginModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "trayPluginModel"
            write: "setTrayPluginModel"
            notify: "trayPluginModelChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "trayItemSurfaceChanged" }
        Signal { name: "trayItemPluginIdChanged" }
        Signal { name: "trayPluginModelChanged" }
        Method { name: "updateTrayItemSurface" }
        Method {
            name: "getTitle"
            type: "QString"
            Parameter { name: "pluginId"; type: "QString" }
        }
        Method {
            name: "isQuickPanelPopup"
            type: "bool"
            Parameter { name: "pluginId"; type: "QString" }
            Parameter { name: "itemKey"; type: "QString" }
        }
        Method { name: "openSystemSettings" }
    }
}
