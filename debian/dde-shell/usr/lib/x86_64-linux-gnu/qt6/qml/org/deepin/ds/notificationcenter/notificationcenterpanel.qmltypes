import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "notifyaccessor.h"
        name: "notifycenter::NotifyAccessor"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["org.deepin.ds.notificationcenter/NotifyAccessor 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Property {
            name: "debugging"
            type: "bool"
            read: "debugging"
            notify: "debuggingChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "entityReceived"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Signal {
            name: "stagingEntityReceived"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Signal {
            name: "stagingEntityClosed"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Signal { name: "debuggingChanged" }
        Method {
            name: "onNotificationStateChanged"
            Parameter { name: "id"; type: "qlonglong" }
            Parameter { name: "processedType"; type: "int" }
        }
        Method {
            name: "onReceivedRecord"
            Parameter { name: "id"; type: "QString" }
        }
        Method { name: "openNotificationSetting" }
    }
    Component {
        file: "notifymodel.h"
        name: "notifycenter::NotifyModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["org.deepin.ds.notificationcenter/NotifyModel 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "dataInfo"
            type: "QString"
            read: "dataInfo"
            notify: "dataInfoChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "collapse"
            type: "bool"
            read: "collapse"
            notify: "collapseChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "dataInfoChanged" }
        Signal { name: "collapseChanged" }
        Signal { name: "countChanged" }
        Method {
            name: "doEntityReceived"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Method { name: "onCountChanged" }
        Method {
            name: "expandApp"
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "collapseApp"
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "remove"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Method {
            name: "removeByApp"
            Parameter { name: "appName"; type: "QString" }
        }
        Method { name: "clear" }
        Method { name: "collapseAllApp" }
        Method { name: "expandAllApp" }
        Method { name: "open" }
        Method { name: "close" }
        Method {
            name: "invokeAction"
            Parameter { name: "id"; type: "qlonglong" }
            Parameter { name: "actionId"; type: "QString" }
        }
        Method {
            name: "pinApplication"
            Parameter { name: "appName"; type: "QString" }
            Parameter { name: "pin"; type: "bool" }
        }
    }
    Component {
        file: "notifystagingmodel.h"
        name: "notifycenter::NotifyStagingModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["org.deepin.ds.notificationcenter/NotifyStagingModel 1.0"]
        exportMetaObjectRevisions: [256]
        Method {
            name: "push"
            Parameter { name: "entity"; type: "NotifyEntity" }
        }
        Method {
            name: "replace"
            Parameter { name: "entity"; type: "NotifyEntity" }
        }
        Method {
            name: "doEntityReceived"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Method {
            name: "onEntityClosed"
            Parameter { name: "id"; type: "qlonglong" }
        }
        Method {
            name: "closeNotify"
            Parameter { name: "id"; type: "qlonglong" }
            Parameter { name: "reason"; type: "int" }
        }
        Method {
            name: "invokeNotify"
            Parameter { name: "id"; type: "qlonglong" }
            Parameter { name: "actionId"; type: "QString" }
        }
        Method { name: "open" }
        Method { name: "close" }
    }
}
