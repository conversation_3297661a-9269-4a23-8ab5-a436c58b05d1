{"magic": "dsg.config.meta", "version": "1.0", "contents": {"quickPlugins": {"value": ["network", "bluetooth", "wireless-casting", "grand-search", "eye-comfort-mode", "airplane-mode", "dnd-mode", "shot-start-plugin", "shot-start-record-plugin", "clipboard", "system-monitor", "media", "dde-brightness", "sound"], "serial": 0, "flags": [], "name": "Quick plugins", "name[zh_CN]": "快捷面板插件", "description[zh_CN]": "指定了插件在快捷面板的显示顺序，从左到右，自上往下，其它的未指定的插件，按照插件的大小，放在最后面（例如：插件占两格，就放在所有两格插件的后面）。\n修改后重启任务栏（注销或者重启电脑亦可）生效）", "description": "", "permissions": "readonly", "visibility": "private"}, "stashedSurfaceIds": {"value": [], "serial": 0, "flags": [], "name": "Stashed Area Surface IDs", "name[zh_CN]": "收起区域标识 ID 列表", "description": "Stashed area tray plugin surface IDs and its sort order", "description[zh_CN]": "托盘收起区域的托盘插件标识 ID 及排序顺序", "permissions": "readwrite", "visibility": "private"}, "collapsableSurfaceIds": {"value": ["application-tray::SNI:Fcitx-0", "bluetooth::bluetooth-item-key", "battery::power", "shutdown::shutdown"], "serial": 0, "flags": [], "name": "Collapsable Area Surface IDs", "name[zh_CN]": "折叠区域标识 ID 列表", "description": "Collapsable area tray plugin surface IDs and its sort order", "description[zh_CN]": "托盘折叠区域的托盘插件标识 ID 及排序顺序", "permissions": "readwrite", "visibility": "private"}, "pinnedSurfaceIds": {"value": ["disk-mount::mount-item-key", "uosai::uosai", "network::network-item-key"], "serial": 0, "flags": [], "name": "Pinned Area Surface IDs", "name[zh_CN]": "固定区域标识 ID 列表", "description": "Pinned area tray plugin surface IDs and its sort order", "description[zh_CN]": "托盘固定（但可拖拽）区域的托盘插件标识 ID 及排序顺序", "permissions": "readwrite", "visibility": "private"}, "hiddenSurfaceIds": {"value": [], "serial": 0, "flags": [], "name": "Hidden Surface IDs", "name[zh_CN]": "需隐藏的标识 ID 列表", "description": "Tray plugin surface IDs that needs to be excluded from any tray area (Quick panel does NOT count as a tray area)", "description[zh_CN]": "不应当在任何托盘区域显示的标识 ID 列表（快捷面板不视为托盘区域）", "permissions": "readwrite", "visibility": "private"}, "isCollapsed": {"value": false, "serial": 0, "flags": [], "name": "Is Collapsed", "name[zh_CN]": "是否折叠", "description": "Current status of the tray collapsable area", "description[zh_CN]": "托盘折叠区域当前的状态", "permissions": "readwrite", "visibility": "private"}, "selfMaintenanceTrayPlugins": {"value": ["libapplication-tray.so", "libbrightness.so", "libdatetime.so", "libdnd-mode.so", "libeye-comfort-mode.so", "libmedia.so", "libnotification.so", "libonboard.so", "libshutdown.so", "libairplane-mode.so", "libbluetooth.so", "libdock-tray-network-plugin.so", "libdock-wirelesscasting-plugin.so", "libkeyboard-layout.so", "libpower.so", "libsound.so"], "serial": 0, "flags": [], "name": "self maintenance plugins", "name[zh_CN]": "自维护托盘插件", "description": "self maintenance plugins", "description[zh_CN]": "自维护托盘插件", "permissions": "readonly", "visibility": "private"}, "subprojectTrayPlugins": {"value": ["libdock-clipboard-plugin.so", "libddegrandsearch_dockplugin.so", "libdeepin-screen-recorder-plugin.so", "libdeepin-system-monitor-plugin.so", "libshot-start-plugin.so", "libshot-start-record-plugin.so", "libdde-disk-mount-plugin.so"], "serial": 0, "flags": [], "name": "subproject tray plugins", "name[zh_CN]": "子项目托盘插件", "description": "subproject tray plugins", "description[zh_CN]": "子项目托盘插件", "permissions": "readwrite", "visibility": "private"}, "crashProneTrayPlugins": {"value": [], "serial": 0, "flags": [], "name": "crash-prone tray plugins", "name[zh_CN]": "易于崩溃的托盘插件", "description": "crash-prone tray plugins", "description[zh_CN]": "易于崩溃的托盘插件", "permissions": "readwrite"}}}