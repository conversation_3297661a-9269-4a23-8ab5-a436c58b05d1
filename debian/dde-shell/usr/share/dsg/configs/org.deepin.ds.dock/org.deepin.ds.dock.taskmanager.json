{"magic": "dsg.config.meta", "version": "1.0", "contents": {"Allow_Force_Quit": {"value": "enabled", "serial": 0, "flags": [], "name": "Allow_Force_Quit", "name[zh_CN]": "强制退出", "description": "allow force quit app ot not", "permissions": "readwrite", "visibility": "private"}, "Window_Split": {"value": "disabled", "serial": 0, "flags": [], "name": "Window_Split", "name[zh_CN]": "窗口拆分", "description": "is window split", "permissions": "readwrite", "visibility": "private"}, "noTaskGrouping": {"value": false, "serial": 0, "flags": [], "name": "No Task-Grouping", "name[zh_CN]": "禁用任务分组", "description": "Disable Taskbar Icon Grouping by Application", "description[zh_CN]": "禁用任务栏图标按应用分组", "permissions": "readwrite", "visibility": "private"}, "Docked_Items": {"value": ["id: dde-file-manager,type: amAPP", "id: org.deepin.browser,type: amAPP", "id: deepin-app-store,type: amAPP", "id: org.deepin.dde.control-center, type: amAPP", "id: deepin-music,type: amAPP", "id: deepin-editor,type: amAPP", "id: deepin-mail,type: amAPP", "id: deepin-terminal,type: amAPP", "id: dde-calendar,type: amAPP", "id: deepin-calculator,type: amAPP"], "serial": 0, "flags": [], "name": "Docked_Items", "name[zh_CN]": "已固定项目", "description": "The default apps which is docked when dock is started.", "permissions": "readwrite", "visibility": "private"}, "dockedElements": {"value": ["desktop/dde-file-manager", "desktop/deepin-app-store", "desktop/org.deepin.browser", "desktop/deepin-mail", "desktop/deepin-terminal", "desktop/dde-calendar", "desktop/deepin-music", "desktop/deepin-editor", "desktop/deepin-calculator", "desktop/org.deepin.dde.control-center"], "serial": 0, "flags": [], "name": "dockedElements", "name[zh_CN]": "任务栏驻留项目", "description": "The items which is docked when dock is started.", "permissions": "readwrite", "visibility": "private"}}}