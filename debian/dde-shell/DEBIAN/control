Package: dde-shell
Version: 2.0.3
Architecture: amd64
Maintainer: Deepin Packages Builder <<EMAIL>>
Installed-Size: 6812
Depends: libc6 (>= 2.34), libdde-shell (= 2.0.3), libdtk6core (>= 6.0), libdtk6gui (>= 6.0), libdtk6widget (>= 6.0), libgcc-s1 (>= 3.0), libicu74 (>= 74.1-1~), libqt6concurrent6 (>= 6.1.2), libqt6core6 (>= 6.8.0), libqt6dbus6 (>= 6.8.0), libqt6gui6 (>= 6.2.0), libqt6qml6 (>= 6.6.0), libqt6quick6 (>= 6.6.0), libqt6sql6 (>= 6.8.0), libqt6waylandclient6 (>= 6.8.0-0deepin6), libqt6waylandcompositor6 (>= 6.8.0-0deepin6), libqt6widgets6 (>= 6.1.2), libstdc++6 (>= 11), libwayland-client0 (>= 1.20.0), libwayland-server0 (>= 1.2.0), libxcb-ewmh2 (>= 0.4.1), libxcb-icccm4 (>= 0.4.1), libxcb-res0 (>= 1.10), libxcb1, libyaml-cpp0.7 (>= 0.7.0), qt6-base-private-abi (= 6.8.0), libqt6svg6, qml6-module-qt-labs-platform, libqt6sql6-sqlite, qt6-wayland (>= 6.8), qml6-module-qtquick-layouts, qml6-module-qtquick-window, libdtk6declarative, qml6-module-qtquick-controls2-styles-chameleon, qml6-module-qt-labs-qmlmodels, dde-tray-loader (>= 1.99.8)
Section: DDE
Priority: optional
Multi-Arch: same
Homepage: https://github.com/linuxdeepin/dde-shell
Description: An wrapper for developed based on dde-shell plugin system
