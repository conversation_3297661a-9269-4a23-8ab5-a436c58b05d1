#----------------------------------------------------------------
# Generated CMake target import file for configuration "None".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "Dde::Shell" for configuration "None"
set_property(TARGET Dde::Shell APPEND PROPERTY IMPORTED_CONFIGURATIONS NONE)
set_target_properties(Dde::Shell PROPERTIES
  IMPORTED_LINK_DEPENDENT_LIBRARIES_NONE "Qt6::Concurrent"
  IMPORTED_LOCATION_NONE "${_IMPORT_PREFIX}/lib/x86_64-linux-gnu/libdde-shell.so.2.0.3"
  IMPORTED_SONAME_NONE "libdde-shell.so.1"
  )

list(APPEND _cmake_import_check_targets Dde::Shell )
list(APPEND _cmake_import_check_files_for_Dde::Shell "${_IMPORT_PREFIX}/lib/x86_64-linux-gnu/libdde-shell.so.2.0.3" )

# Import target "Dde::dde-shell-plugin" for configuration "None"
set_property(TARGET Dde::dde-shell-plugin APPEND PROPERTY IMPORTED_CONFIGURATIONS NONE)
set_target_properties(Dde::dde-shell-plugin PROPERTIES
  IMPORTED_LOCATION_NONE "${_IMPORT_PREFIX}/lib/x86_64-linux-gnu/qt6/qml/org/deepin/ds/libdde-shell-plugin.so"
  IMPORTED_SONAME_NONE "libdde-shell-plugin.so"
  )

list(APPEND _cmake_import_check_targets Dde::dde-shell-plugin )
list(APPEND _cmake_import_check_files_for_Dde::dde-shell-plugin "${_IMPORT_PREFIX}/lib/x86_64-linux-gnu/qt6/qml/org/deepin/ds/libdde-shell-plugin.so" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
