
####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was DDEShellConfig.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../../" ABSOLUTE)

# Use original install prefix when loaded through a "/usr move"
# cross-prefix symbolic link such as /lib -> /usr/lib.
get_filename_component(_realCurr "${CMAKE_CURRENT_LIST_DIR}" REALPATH)
get_filename_component(_realOrig "/usr/lib/x86_64-linux-gnu/cmake/DDEShell" REALPATH)
if(_realCurr STREQUAL _realOrig)
  set(PACKAGE_PREFIX_DIR "/usr")
endif()
unset(_realOrig)
unset(_realCurr)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################
include(CMakeFindDependencyMacro)
find_dependency(Dtk6Core)
find_dependency(Dtk6Gui)
find_package(Qt6 COMPONENTS Qml Quick REQUIRED)

include(${CMAKE_CURRENT_LIST_DIR}/DDEShellTargets.cmake)
set(DDE_SHELL_PACKAGE_INSTALL_DIR /usr/share/dde-shell)
set(DDE_SHELL_PLUGIN_INSTALL_DIR /usr/lib/x86_64-linux-gnu/dde-shell)
set(DDE_SHELL_TRANSLATION_INSTALL_DIR /usr/share/dde-shell)
check_required_components(Dtk6Core)

include("${CMAKE_CURRENT_LIST_DIR}/DDEShellPackageMacros.cmake")
