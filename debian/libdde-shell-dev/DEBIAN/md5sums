00a002ad46bfa2cf24b31581ed803635  usr/include/dde-shell/applet.h
afea1f1d66ad29d07c46abc4627a27af  usr/include/dde-shell/appletbridge.h
e230694db5a8bc37e7ce0640ac5c6b56  usr/include/dde-shell/appletdata.h
7dba875dfffdd2a4a39b61b9a2556784  usr/include/dde-shell/appletproxy.h
71ede6fe93d1f701f159825f91ea1071  usr/include/dde-shell/containment.h
061dd30890c1a73ac548fe19490b1870  usr/include/dde-shell/dlayershellwindow.h
b14b402fbb57e8a9d236d5420f4481b1  usr/include/dde-shell/dsglobal.h
692b8e0d1cc9cb140f5811abb9d3ffff  usr/include/dde-shell/dstypes.h
cdfd07f3b35f2bddc63d46e62f1fed0a  usr/include/dde-shell/dsutility.h
de3f262ba1c2d16e6c7fd10030f5ff44  usr/include/dde-shell/listtotableproxymodel.h
9d22d4fef499313680ace81909cc20dd  usr/include/dde-shell/panel.h
39de543ec8205a81919af3be8cd56145  usr/include/dde-shell/pluginfactory.h
58fdf4a08c9181991d0d2458c96ffd9e  usr/include/dde-shell/pluginloader.h
16ed5e8fe5dd2e3b964b7ab7586cadd3  usr/include/dde-shell/pluginmetadata.h
e4e9c43888b9ded17585cea0fc0023ee  usr/include/dde-shell/qmlengine.h
c9988932061b7a6031d187ef1e8aefa0  usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellConfig.cmake
897dae142271036ffbb5ab216207711f  usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellConfigVersion.cmake
a724acaf078a237ec10e0fc4802b560c  usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellPackageMacros.cmake
c93a1c3fbb61046268f8870ee8f3e181  usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellTargets-none.cmake
56bc52305d5d8fed5def40d636b4fe48  usr/lib/x86_64-linux-gnu/cmake/DDEShell/DDEShellTargets.cmake
9baa17ff4026b4a1201b7e500f243391  usr/share/doc/libdde-shell-dev/changelog.gz
b04c4cef31fe73ce63500521805f6a3d  usr/share/doc/libdde-shell-dev/copyright
